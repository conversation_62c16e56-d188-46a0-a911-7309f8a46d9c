/**
 * 统一积木数据管理器
 * 整合所有积木数据的获取、存储、同步逻辑
 */

class BrickManager {
  constructor() {
    this.initialized = false;
    this.bricks = [];
    this.lastSyncTime = null;
    this.syncInProgress = false;
  }

  /**
   * 初始化积木管理器
   */
  async init() {
    if (this.initialized) return;

    console.log('🧱 初始化积木数据管理器...');

    try {
      await this.loadBricks();
      this.initialized = true;
      console.log('✅ 积木数据管理器初始化完成');
    } catch (error) {
      console.error('❌ 积木数据管理器初始化失败:', error);
    }
  }

  /**
   * 统一加载积木数据
   * 优先级：全局数据 > 本地存储 > resumeTasks提取 > 云数据库 > 默认数据
   */
  async loadBricks() {
    console.log('📦 开始加载积木数据...');

    // 1. 尝试从全局数据获取
    const app = getApp();
    if (app.globalData && app.globalData.bricks && app.globalData.bricks.length > 0) {
      this.bricks = this.normalizeBricks(app.globalData.bricks);
      console.log(`✅ 从全局数据获取积木: ${this.bricks.length} 个`);
      return this.bricks;
    }

    // 2. 尝试从本地存储获取
    try {
      const localBricks = wx.getStorageSync('bricks');
      if (localBricks && localBricks.length > 0) {
        this.bricks = this.normalizeBricks(localBricks);
        console.log(`✅ 从本地存储获取积木: ${this.bricks.length} 个`);
        this.updateGlobalData();
        return this.bricks;
      }
    } catch (error) {
      console.warn('⚠️ 本地存储获取积木失败:', error);
    }

    // 3. 从resumeTasks提取积木数据（核心修复）
    try {
      const extractedBricks = await this.extractBricksFromTasks();
      if (extractedBricks.length > 0) {
        this.bricks = this.normalizeBricks(extractedBricks);
        console.log(`✅ 从resumeTasks提取积木: ${this.bricks.length} 个`);
        this.saveToLocal();
        this.updateGlobalData();
        return this.bricks;
      }
    } catch (error) {
      console.warn('⚠️ 从resumeTasks提取积木失败:', error);
    }

    // 4. 尝试从云数据库bricks集合获取
    try {
      const cloudBricks = await this.loadFromCloudDatabase();
      if (cloudBricks.length > 0) {
        this.bricks = this.normalizeBricks(cloudBricks);
        console.log(`✅ 从云数据库获取积木: ${this.bricks.length} 个`);
        this.saveToLocal();
        this.updateGlobalData();
        return this.bricks;
      }
    } catch (error) {
      console.warn('⚠️ 云数据库获取积木失败:', error);
    }

    // 5. 使用默认积木数据
    console.log('🔄 使用默认积木数据');
    this.bricks = this.normalizeBricks(this.getDefaultBricks());
    this.updateGlobalData();
    return this.bricks;
  }

  /**
   * 从resumeTasks中提取积木数据（核心功能）
   */
  async extractBricksFromTasks() {
    console.log('🔍 开始从resumeTasks提取积木数据...');

    const userId = wx.getStorageSync('userId') || wx.getStorageSync('openid');
    if (!userId) {
      console.warn('⚠️ 未找到用户ID，无法提取积木数据');
      return [];
    }

    try {
      const db = wx.cloud.database();
      const tasksResult = await db.collection('resumeTasks').where({
        userId: userId,
        status: 'completed'
      }).orderBy('completedAt', 'desc').get();

      console.log(`📋 找到 ${tasksResult.data.length} 个已完成的任务`);

      const extractedBricks = [];
      for (const task of tasksResult.data) {
        if (task.result && task.result.data && task.result.data.bricks) {
          const taskBricks = task.result.data.bricks.map(brick => ({
            ...brick,
            // 确保必要字段
            id: brick.id || `brick_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            sourceTaskId: task.taskId,
            extractedAt: new Date().toISOString(),
            userId: userId
          }));
          extractedBricks.push(...taskBricks);
        }
      }

      console.log(`🎯 成功提取 ${extractedBricks.length} 个积木`);
      return extractedBricks;

    } catch (error) {
      console.error('❌ 从resumeTasks提取积木失败:', error);
      return [];
    }
  }

  /**
   * 从云数据库bricks集合加载
   */
  async loadFromCloudDatabase() {
    try {
      const db = wx.cloud.database();
      const userId = wx.getStorageSync('userId') || wx.getStorageSync('openid');

      let query = db.collection('bricks');
      if (userId) {
        query = query.where({ userId });
      }

      const result = await query.limit(100).orderBy('createTime', 'desc').get();
      return result.data || [];
    } catch (error) {
      console.error('❌ 云数据库加载积木失败:', error);
      return [];
    }
  }

  /**
   * 获取默认积木数据
   */
  getDefaultBricks() {
    return [
      {
        id: 'default_1',
        category: 'personal',
        title: '个人信息',
        description: '请上传简历或手动添加个人信息，包括姓名、联系方式、地址等基本信息',
        content: '暂无积木数据',
        type: 'text',
        keywords: ['个人信息', '联系方式', '基本信息'],
        confidence: 1.0,
        usageCount: 0,
        level: '基础',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
        source: 'default',
        isDefault: true
      }
    ];
  }

  /**
   * 保存到本地存储
   */
  saveToLocal() {
    try {
      wx.setStorageSync('bricks', this.bricks);
      wx.setStorageSync('bricks_sync_time', new Date().toISOString());
      console.log('✅ 积木数据已保存到本地存储');
    } catch (error) {
      console.error('❌ 保存积木到本地存储失败:', error);
    }
  }

  /**
   * 更新全局数据
   */
  updateGlobalData() {
    try {
      const app = getApp();
      if (app.globalData) {
        app.globalData.bricks = this.bricks;
        console.log('✅ 积木数据已更新到全局数据');
      }
      if (app.store) {
        app.store.setState('bricks.list', this.bricks);
        console.log('✅ 积木数据已更新到状态管理器');
      }
    } catch (error) {
      console.error('❌ 更新全局数据失败:', error);
    }
  }

  /**
   * 标准化积木数据结构
   */
  normalizeBrick(brick) {
    return {
      ...brick,
      // 确保必要字段存在
      id: brick.id || `brick_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      title: brick.title || brick.content || '未命名积木',
      description: brick.description || brick.content || '暂无描述',
      category: brick.category || 'other',
      keywords: brick.keywords || [],
      confidence: brick.confidence || 0.8,
      usageCount: brick.usageCount || 0,
      level: brick.level || '基础',
      createTime: brick.createTime || new Date().toISOString(),
      updateTime: brick.updateTime || new Date().toISOString(),
      source: brick.source || 'unknown'
    };
  }

  /**
   * 标准化积木数组
   */
  normalizeBricks(bricks) {
    if (!Array.isArray(bricks)) return [];
    return bricks.map(brick => this.normalizeBrick(brick));
  }

  /**
   * 获取积木数据
   */
  async getBricks() {
    if (!this.initialized) {
      await this.init();
    }
    // 返回前进行数据标准化
    this.bricks = this.normalizeBricks(this.bricks);
    return this.bricks;
  }

  /**
   * 获取积木数量
   */
  async getBricksCount() {
    const bricks = await this.getBricks();
    return bricks.length;
  }

  /**
   * 强制刷新积木数据
   */
  async refresh() {
    console.log('🔄 强制刷新积木数据...');
    this.bricks = [];
    this.initialized = false;
    await this.init();
    return this.bricks;
  }

  /**
   * 清理并修复积木数据
   */
  async cleanAndRepair() {
    console.log('🧹 开始清理并修复积木数据...');

    try {
      // 清理本地存储中的旧数据
      wx.removeStorageSync('bricks');
      wx.removeStorageSync('bricks_sync_time');

      // 清理全局数据
      const app = getApp();
      if (app.globalData) {
        app.globalData.bricks = [];
      }

      // 重新初始化
      this.bricks = [];
      this.initialized = false;
      await this.init();

      console.log('✅ 积木数据清理修复完成');
      return this.bricks;
    } catch (error) {
      console.error('❌ 积木数据清理修复失败:', error);
      return [];
    }
  }

  /**
   * 添加新积木
   */
  async addBrick(brick) {
    const newBrick = {
      ...brick,
      id: brick.id || `brick_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createTime: new Date().toISOString(),
      userId: wx.getStorageSync('userId') || wx.getStorageSync('openid')
    };

    this.bricks.push(newBrick);
    this.saveToLocal();
    this.updateGlobalData();

    console.log('✅ 新积木已添加:', newBrick.id);
    return newBrick;
  }
}

// 创建全局单例
const brickManager = new BrickManager();

module.exports = brickManager;
