// pages/bricks/bricks.js
// 安全获取ApiService的方法
function getApiService() {
  // 尝试多种方式获取ApiService
  if (global.ApiService) {
    return global.ApiService;
  }

  try {
    const app = getApp();
    if (app && app.ApiService) {
      return app.ApiService;
    }
  } catch (e) {
    console.warn('无法获取app实例:', e);
  }

  // 如果都获取不到，返回null并在使用时给出警告
  console.error('ApiService不可用，请检查是否正确加载了utils/api.js');
  return null;
}

// 安全获取HttpApiService的方法
function getHttpApiService() {
  // 尝试多种方式获取HttpApiService
  if (global.HttpApiService) {
    return global.HttpApiService;
  }

  try {
    const app = getApp();
    if (app && app.HttpApiService) {
      return app.HttpApiService;
    }
  } catch (e) {
    console.warn('无法获取app实例:', e);
  }

  // 如果都获取不到，返回null并在使用时给出警告
  console.error('HttpApiService不可用，请检查是否正确加载了utils/http-api.js');
  return null;
}

// 从全局获取ApiService，兼容微信小程序
const ApiService = getApiService()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    bricks: [],
    filteredBricks: [],
    resumes: [],
    filteredResumes: [],

    currentFilter: 'all',
    showSearch: false,
    isSelectMode: false,
    selectedItems: [],
    loading: false,
    loadingText: '',

    // 上传相关
    uploading: false,
    uploadProgress: 0,

    // 详情弹窗相关
    showBrickDetail: false,
    currentBrick: null,

    // 统计数据
    totalCount: 0,
    resumeCount: 0,
    personalCount: 0,
    educationCount: 0,
    experienceCount: 0,
    skillCount: 0,
    projectCount: 0,
    matchCount: 0,

    // 编辑相关
    editingBrick: null,
    showEditModal: false,

    // 分页相关
    hasMore: true,
    page: 1,
    pageSize: 20,
    categories: [
      { id: 'all', name: '全部', icon: '📋' },
      { id: '技术能力', name: '技术能力', icon: '💻' },
      { id: '管理能力', name: '管理能力', icon: '📊' },
      { id: '软技能', name: '软技能', icon: '🤝' }
    ],
    selectedCategory: 'all',
    isLoading: false,
    categoryStats: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('积木库页面加载', options)

    // 延迟检查登录状态，避免页面加载时的时序问题
    setTimeout(() => {
      this.checkLoginStatus()
    }, 100)
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('积木库页面显示')
    this.cleanupMockData()
    this.loadBricksList()
    this.loadStats()
    // 确保数据同步
    this.ensureDataConsistency()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore()
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '我的能力积木库 - 职涯积木',
      path: '/pages/bricks/bricks'
    }
  },

  // 初始化页面
  initPage() {
    this.loadBricksList()
  },

  // 检查是否需要数据修复
  async checkIfNeedsDataRepair() {
    try {
      // 检查本地存储中的积木数据
      const localBricks = wx.getStorageSync('bricks') || []

      // 检查是否有积木缺少必要字段
      const hasIncompleteData = localBricks.some(brick =>
        !brick.title || !brick.description ||
        (brick.category === 'personal' && brick.content === '暂无积木数据' && !brick.title)
      )

      if (hasIncompleteData) {
        console.log('🔍 检测到不完整的积木数据，需要修复')
        return true
      }

      // 检查全局数据
      const app = getApp()
      if (app.globalData && app.globalData.bricks) {
        const hasIncompleteGlobalData = app.globalData.bricks.some(brick =>
          !brick.title || !brick.description
        )
        if (hasIncompleteGlobalData) {
          console.log('🔍 检测到全局数据不完整，需要修复')
          return true
        }
      }

      return false
    } catch (error) {
      console.warn('⚠️ 检查数据修复需求失败:', error)
      return false
    }
  },

  // 清理模拟数据
  cleanupMockData() {
    try {
      const app = getApp()
      let dataChanged = false

      // 清理模拟简历数据
      if (app.globalData.resumes) {
        const mockResumeIds = ['resume_001', 'resume_002', 'resume_003']
        const originalLength = app.globalData.resumes.length
        const cleanedResumes = app.globalData.resumes.filter(resume =>
          !mockResumeIds.includes(resume.id)
        )
        app.globalData.resumes = cleanedResumes

        if (originalLength !== cleanedResumes.length) {
          dataChanged = true
          console.log('🧹 清理模拟简历数据，剩余:', cleanedResumes.length, '个简历')
        }
      }

      // 清理本地存储中的模拟简历数据
      const localResumes = wx.getStorageSync('resumes') || []
      if (localResumes.length > 0) {
        const mockResumeIds = ['resume_001', 'resume_002', 'resume_003']
        const originalLength = localResumes.length
        const cleanedLocalResumes = localResumes.filter(resume =>
          !mockResumeIds.includes(resume.id)
        )

        if (originalLength !== cleanedLocalResumes.length) {
          wx.setStorageSync('resumes', cleanedLocalResumes)
          dataChanged = true
          console.log('🧹 清理本地模拟简历数据，剩余:', cleanedLocalResumes.length, '个简历')
        }
      }

      // 如果数据发生变化，更新页面数据和计数器
      if (dataChanged) {
        this.setData({
          resumes: app.globalData.resumes || []
        })
        // 注意：这里不调用updateCounts，因为会在loadBricksList中调用
        console.log('🔄 数据清理完成，将在数据加载时更新计数器')
      }

    } catch (error) {
      console.error('清理模拟数据失败:', error)
    }
  },

  // 返回上一页
  goBack() {
    try {
      wx.navigateBack({
        delta: 1,
        success: () => {
          console.log('返回上一页成功')
        },
        fail: (err) => {
          console.error('返回上一页失败:', err)
          // 如果返回失败，跳转到首页
          wx.navigateTo({
            url: '/pages/index/index'
          })
        }
      })
    } catch (error) {
      console.error('返回按钮点击失败:', error)
      wx.showToast({
        title: '返回失败',
        icon: 'error'
      })
    }
  },

  // 切换搜索栏
  toggleSearch() {
    this.setData({
      showSearch: !this.data.showSearch
    })
  },

  // 加载积木列表 - 使用统一的BrickManager
  async loadBricksList() {
    console.log('开始加载积木列表')
    this.setData({ loading: true, loadingText: '加载积木中...' })

    try {
      // 使用统一的积木管理器
      const BrickManager = require('../../utils/brick-manager.js')

      // 🔧 修复：检查是否需要清理修复数据
      const needsRepair = await this.checkIfNeedsDataRepair()
      if (needsRepair) {
        console.log('🧹 检测到数据需要修复，开始清理...')
        this.setData({ loadingText: '正在修复数据...' })
        await BrickManager.cleanAndRepair()
      }

      let bricksData = await BrickManager.getBricks()

      console.log('✅ 通过BrickManager获取积木数据:', bricksData.length)

      if (Array.isArray(bricksData) && bricksData.length > 0) {
        // 🔧 清理"技能特长"空积木
        const originalLength = bricksData.length
        bricksData = bricksData.filter(brick => {
          const isSkillSpecialtyEmpty = brick.title === '技能特长' && (!brick.description || brick.description.trim() === '')
          if (isSkillSpecialtyEmpty) {
            console.log('🗑️ 删除空的"技能特长"积木:', brick)
            return false
          }
          return true
        })

        // 处理积木数据 - 修复：保持原始category值，不进行转换
        const processedBricks = bricksData.map(brick => ({
          ...brick,
          selected: false,
          icon: this.getBrickIcon(brick.category),
          // 🔧 修复：保持原始category值，避免英文转中文导致的分类问题
          // category: this.getCategoryName(brick.category), // 注释掉这行
          matchCount: brick.usageCount || 0,
          createTime: this.formatDate(brick.createTime) || '2024-01-01',
          updateTime: this.formatDate(brick.updateTime)
        }))

        this.setData({
          bricks: processedBricks,
          filteredBricks: processedBricks,
          loading: false
        })

        // 更新统计和筛选
        this.updateCounts()
        this.filterBricks()

        console.log('✅ 成功通过BrickManager加载积木数据')
        return
      } else {
        console.log('⚠️ 没有找到积木数据，显示空状态')
        this.setData({
          bricks: [],
          filteredBricks: [],
          loading: false
        })
      }

    } catch (error) {
      console.error('❌ 通过BrickManager加载积木数据失败:', error)
      this.setData({
        bricks: [],
        filteredBricks: [],
        loading: false
      })
    }
  },

  // 查看积木详情
  viewBrickDetail(e) {
    console.log('查看积木详情', e)
    const id = e.currentTarget.dataset.id
    const brick = this.data.bricks.find(b => b.id === id || b._id === id)

    if (brick) {
      console.log('找到积木:', brick)
      this.setData({
        currentBrick: brick,
        showBrickDetail: true
      })
    } else {
      console.error('未找到积木:', id)
      wx.showToast({
        title: '积木不存在',
        icon: 'error'
      })
    }
  },

  // 查看简历详情
  viewResumeDetail(e) {
    console.log('查看简历详情', e)
    const id = e.currentTarget.dataset.id
    const resume = this.data.resumes.find(r => r.id === id || r._id === id)

    if (resume) {
      // 跳转到简历预览页面
      wx.navigateTo({
        url: `/pages/preview/preview?id=${id}`,
        success: () => {
          console.log('跳转到简历预览页面')
        },
        fail: (err) => {
          console.error('跳转失败:', err)
          wx.showToast({
            title: '页面跳转失败',
            icon: 'error'
          })
        }
      })
    } else {
      console.error('未找到简历:', id)
      wx.showToast({
        title: '简历不存在',
        icon: 'error'
      })
    }
  },

  // 关闭积木详情弹窗
  closeBrickDetail() {
    this.setData({
      showBrickDetail: false,
      currentBrick: null
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 编辑当前积木
  editCurrentBrick() {
    if (this.data.currentBrick) {
      const id = this.data.currentBrick.id || this.data.currentBrick._id
      this.closeBrickDetail()
      wx.navigateTo({
        url: `/pages/edit/edit?type=brick&id=${id}`
      })
    }
  },

  // 删除当前积木
  deleteCurrentBrick() {
    if (!this.data.currentBrick) {
      wx.showToast({
        title: '积木不存在',
        icon: 'error'
      })
      return
    }

    const brick = this.data.currentBrick

    wx.showModal({
      title: '确认删除',
      content: `确定要删除积木"${brick.title}"吗？`,
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.performDeleteBrick(brick)
        }
      }
    })
  },

  // 执行删除积木操作
  async performDeleteBrick(brick) {
    try {
      // 安全获取ApiService
      const apiService = getApiService()

      if (!apiService || typeof apiService.deleteBrick !== 'function') {
        console.warn('ApiService.deleteBrick不可用，仅删除本地数据')
        throw new Error('deleteBrick方法不可用')
      }

      const result = await apiService.deleteBrick(brick._id || brick.id)

      if (result.success) {
        // 关闭详情弹窗
        this.closeBrickDetail()

        // 从数据中删除
        const bricks = this.data.bricks.filter(b =>
          (b.id !== brick.id && b._id !== brick._id) &&
          (b.id !== brick._id && b._id !== brick.id)
        )

        this.setData({ bricks })
        this.filterBricks()
        this.updateCounts()

        // 🔧 修复：通过状态管理器更新数据
        const app = getApp()
        const store = app.getStore()

        if (store) {
          store.setBricks(bricks)
        } else {
          app.globalData.bricks = bricks
        }

        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
      }
    } catch (error) {
      console.error('删除积木失败:', error)

      // API调用失败时，仍然删除本地数据
      this.closeBrickDetail()

      const bricks = this.data.bricks.filter(b =>
        (b.id !== brick.id && b._id !== brick._id) &&
        (b.id !== brick._id && b._id !== brick.id)
      )

      this.setData({ bricks })
      this.filterBricks()
      this.updateCounts()

      // 🔧 修复：通过状态管理器更新数据
      const app = getApp()
      const store = app.getStore()

      if (store) {
        store.setBricks(bricks)
      } else {
        app.globalData.bricks = bricks
      }

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })
    }
  },

  // 格式化日期
  formatDate(dateStr) {
    if (!dateStr) return ''

    try {
      const date = new Date(dateStr)
      if (isNaN(date.getTime())) return dateStr

      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    } catch (error) {
      console.error('日期格式化失败:', error)
      return dateStr
    }
  },

  // 进入选择模式
  enterSelectMode() {
    this.setData({
      isSelectMode: true,
      selectedItems: []
    })
  },

  // 取消选择模式
  cancelSelect() {
    // 清除所有选中状态
    const bricks = this.data.bricks.map(brick => ({
      ...brick,
      selected: false
    }))

    const resumes = this.data.resumes.map(resume => ({
      ...resume,
      selected: false
    }))

    this.setData({
      bricks: bricks,
      resumes: resumes,
      isSelectMode: false,
      selectedItems: []
    })
    this.filterBricks()
    this.filterResumes()
  },

  // 切换选中状态
  toggleSelect(e) {
    console.log('切换选中状态', e)
    const id = e.currentTarget.dataset.id
    const type = e.currentTarget.dataset.type

    if (type === 'brick') {
      const bricks = this.data.bricks.map(brick => {
        if ((brick.id === id || brick._id === id)) {
          return { ...brick, selected: !brick.selected }
        }
        return brick
      })

      const selectedItems = bricks.filter(brick => brick.selected)

      this.setData({
        bricks: bricks,
        selectedItems
      })
      this.filterBricks()
    } else if (type === 'resume') {
      const resumes = this.data.resumes.map(resume => {
        if ((resume.id === id || resume._id === id)) {
          return { ...resume, selected: !resume.selected }
        }
        return resume
      })

      const selectedItems = resumes.filter(resume => resume.selected)

      this.setData({
        resumes: resumes,
        selectedItems
      })
      this.filterResumes()
    }
  },

  // 删除选中积木
  deleteSelected() {
    const selectedIds = this.data.selectedItems.map(item => item.id || item._id)

    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedIds.length} 个项目吗？`,
      success: (res) => {
        if (res.confirm) {
          const bricks = this.data.bricks.filter(brick => !selectedIds.includes(brick.id) && !selectedIds.includes(brick._id))
          const resumes = this.data.resumes.filter(resume => !selectedIds.includes(resume.id) && !selectedIds.includes(resume._id))

          // 🔧 修复：通过状态管理器更新数据
          const app = getApp()
          const store = app.getStore()

          if (store) {
            store.setBricks(bricks)
          } else {
            app.globalData.bricks = bricks
          }

          this.setData({
            bricks: bricks,
            resumes: resumes,
            isSelectMode: false,
            selectedItems: []
          })
          this.updateCounts()
          this.filterBricks()
          this.filterResumes()

          wx.showToast({
            title: '删除成功',
            icon: 'success'
          })
        }
      }
    })
  },

  // 加载简历列表
  loadResumesList() {
    console.log('开始加载简历列表')
    const app = getApp()
    let resumes = app.globalData.resumes || []

    // 🔧 修复：不再自动加载模拟数据，保持空数组
    console.log('当前简历数据:', resumes.length, '个简历')

    const processedResumes = resumes.map(resume => ({
      ...resume,
      selected: false,
      icon: this.getResumeIcon(resume.status),
      status: this.getStatusName(resume.status),
      updateTime: resume.updateTime || '2024-01-01',
      viewCount: resume.viewCount || 0
    }))

    console.log('处理后的简历数据:', processedResumes)

    this.setData({
      resumes: processedResumes,
      filteredResumes: processedResumes
    })

    this.updateCounts()
    this.filterResumes()

    console.log('简历列表加载完成')
  },

  // 获取积木图标
  getBrickIcon(category) {
    const iconMap = {
      '技术能力': '💻',
      '管理能力': '📊',
      '软技能': '🤝',
      '个人信息': '👤',
      '工作经历': '💼',
      '教育背景': '🎓',
      '技能证书': '💻',
      '项目经验': '🚀'
    }
    return iconMap[category] || '🧱'
  },

  // 获取分类名称
  getCategoryName(category) {
    const nameMap = {
      'skill': '技能',
      'experience': '经验',
      'project': '项目',
      'education': '教育',
      'personal': '个人'
    }
    return nameMap[category] || category || '其他'
  },

  // 获取简历图标
  getResumeIcon(status) {
    const iconMap = {
      'draft': '📝',
      'published': '✅',
      'template': '📋',
      'archived': '📁'
    }
    return iconMap[status] || '📄'
  },

  // 获取状态名称
  getStatusName(status) {
    const nameMap = {
      'draft': '草稿',
      'published': '已发布',
      'template': '模板',
      'archived': '已归档'
    }
    return nameMap[status] || status || '未知'
  },

  // 获取模拟积木数据
  getMockBricks() {
    return [
      {
        id: 'brick_001',
        title: 'React 开发经验',
        description: '熟练掌握 React 开发，包括 Hooks、Redux、React Router 等生态系统',
        category: '技术能力',
        keywords: ['React', 'JavaScript', '前端开发', 'Hooks', 'Redux'],
        confidence: 0.9,
        usageCount: 5,
        level: '高级',
        createTime: '2024-01-15',
        updateTime: '2024-01-20'
      },
      {
        id: 'brick_002',
        title: '团队管理',
        description: '具备5年以上团队管理经验，擅长项目协调和人员管理',
        category: '管理能力',
        keywords: ['团队管理', '项目管理', '领导力', '沟通协调'],
        confidence: 0.85,
        usageCount: 3,
        level: '高级',
        createTime: '2024-01-10',
        updateTime: '2024-01-18'
      },
      {
        id: 'brick_003',
        title: '沟通协调能力',
        description: '优秀的跨部门沟通协调能力，善于处理复杂的人际关系',
        category: '软技能',
        keywords: ['沟通', '协调', '人际关系', '合作'],
        confidence: 0.8,
        usageCount: 8,
        level: '中级',
        createTime: '2024-01-05',
        updateTime: '2024-01-22'
      },
      {
        id: 'brick_004',
        title: 'Node.js 后端开发',
        description: '熟练使用 Node.js 进行后端开发，包括 Express、MongoDB 等技术栈',
        category: '技术能力',
        keywords: ['Node.js', 'Express', 'MongoDB', '后端开发', 'API'],
        confidence: 0.88,
        usageCount: 4,
        level: '高级',
        createTime: '2024-01-12',
        updateTime: '2024-01-25'
      },
      {
        id: 'brick_005',
        title: '数据分析能力',
        description: '具备数据收集、清洗、分析和可视化的完整技能链',
        category: '技术能力',
        keywords: ['数据分析', 'Python', 'SQL', 'Excel', '可视化'],
        confidence: 0.75,
        usageCount: 2,
        level: '中级',
        createTime: '2024-01-08',
        updateTime: '2024-01-20'
      }
    ]
  },

  // 获取模拟简历数据 - 已清空模拟数据
  getMockResumes() {
    return []
  },

  // 更新统计数据 - 调试增强版
  updateCounts() {
    const bricks = this.data.bricks || []
    const resumes = this.data.resumes || []

    // 🔍 调试：打印所有积木的category信息
    console.log('🔍 调试积木分类信息:')
    const categoryStats = {}
    bricks.forEach((brick, index) => {
      const category = brick.category || 'undefined'
      categoryStats[category] = (categoryStats[category] || 0) + 1
      // 打印所有积木的详细信息，不限制数量
      console.log(`积木${index}: title="${brick.title}", category="${brick.category}", type="${brick.type}"`)
    })
    console.log('📊 分类统计:', categoryStats)

    // 按类别统计积木数量 - 修复分类逻辑
    const personalBricks = bricks.filter(brick =>
      brick.category === 'personal' ||
      brick.category === '个人' ||
      brick.category === '个人信息' ||
      brick.category === 'personalInfo' ||
      brick.type === 'personal' ||
      brick.type === 'personalInfo'
    );
    const personalCount = personalBricks.length;
    console.log('👤 个人信息积木:', personalBricks.map(b => `${b.title}(${b.category})`));

    const educationBricks = bricks.filter(brick =>
      brick.category === 'education' ||
      brick.category === '教育' ||
      brick.category === '教育背景' ||
      brick.category === 'educationBackground' ||
      brick.type === 'education' ||
      brick.type === 'educationBackground'
    );
    const educationCount = educationBricks.length;
    console.log('🎓 教育背景积木:', educationBricks.map(b => `${b.title}(${b.category})`));

    const experienceBricks = bricks.filter(brick =>
      brick.category === 'experience' ||
      brick.category === '经验' ||
      brick.category === '工作经历' ||
      brick.category === 'workExperience' ||
      brick.category === 'work' ||
      brick.type === 'experience' ||
      brick.type === 'workExperience'
    );
    const experienceCount = experienceBricks.length;
    console.log('💼 工作经历积木:', experienceBricks.map(b => `${b.title}(${b.category})`));

    // 修复：技能积木只包含技能相关，不包含项目
    const skillBricks = bricks.filter(brick =>
      brick.category === 'skills' ||
      brick.category === '技能' ||
      brick.category === '技术能力' ||
      brick.category === 'skill' ||
      brick.category === 'abilities' ||
      brick.type === 'skills' ||
      brick.type === 'skill'
    );
    const skillCount = skillBricks.length;
    console.log('🔧 技能积木:', skillBricks.map(b => `${b.title}(${b.category})`));

    // 修复：项目积木单独统计，同时匹配英文和中文
    const projectBricks = bricks.filter(brick =>
      brick.category === 'project' ||
      brick.category === '项目' ||
      brick.category === '项目经验' ||
      brick.category === 'projects' ||
      brick.type === 'project' ||
      brick.type === 'projects'
    );
    const projectCount = projectBricks.length;
    console.log('🚀 项目积木:', projectBricks.map(b => `${b.title}(${b.category})`));

    // 计算能力积木总数（技能+项目）
    const abilityCount = skillCount + projectCount;
    console.log(`🧱 能力积木总数: ${abilityCount} = ${skillCount}(技能) + ${projectCount}(项目)`);

    this.setData({
      totalCount: bricks.length,
      resumeCount: resumes.length,
      personalCount: personalCount,
      educationCount: educationCount,
      experienceCount: experienceCount,
      skillCount: abilityCount, // 显示为能力积木总数
      projectCount: projectCount,
      matchCount: bricks.reduce((sum, brick) => sum + (brick.matchCount || brick.usageCount || 0), 0)
    })

    console.log('📊 统计数据更新完成:', {
      totalCount: bricks.length,
      resumeCount: resumes.length,
      personalCount,
      educationCount,
      experienceCount,
      skillCount: abilityCount,
      projectCount,
      实际技能积木: skillCount,
      实际项目积木: projectCount
    })
  },

  // 筛选积木
  filterBricks() {
    const { bricks, currentFilter } = this.data
    let filtered = bricks

    // 按分类筛选
    if (currentFilter !== 'all' && currentFilter !== 'bricks' && currentFilter !== 'resumes') {
      filtered = filtered.filter(brick => {
        switch (currentFilter) {
          case 'personal':
            return brick.category === 'personal' || brick.category === '个人' || brick.category === '个人信息'
          case 'education':
            return brick.category === 'education' || brick.category === '教育' || brick.category === '教育背景'
          case 'experience':
            return brick.category === 'experience' || brick.category === '经验' || brick.category === '工作经历'
          case 'skills':
            return brick.category === 'skills' || brick.category === '技能' || brick.category === '技术能力' || brick.category === '技能证书' || brick.category === 'project' || brick.category === '项目' || brick.category === '项目经验'
          case 'skill':
            return brick.category === 'skills' || brick.category === '技能' || brick.category === '技术能力'
          case 'project':
            return brick.category === 'project' || brick.category === '项目' || brick.category === '项目经验'
          default:
            return true
        }
      })
    }



    this.setData({ filteredBricks: filtered })
  },

  // 筛选简历
  filterResumes() {
    const { resumes, currentFilter } = this.data
    let filtered = resumes



    this.setData({ filteredResumes: filtered })
  },



  // 设置筛选条件
  setFilter(e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({ currentFilter: filter })
    this.filterBricks()
    this.filterResumes()
  },

  // 添加积木
  async addBrick() {
    const brickData = {
      title: '新积木',
      description: '请描述您的能力和经验',
      category: '技术能力',
      icon: '💻',
      keywords: []
    }

    try {
      // 安全获取ApiService
      const apiService = getApiService()

      if (!apiService || typeof apiService.addBrick !== 'function') {
        console.warn('ApiService.addBrick不可用，请使用编辑页面添加积木')
        wx.showToast({
          title: '请使用编辑页面添加',
          icon: 'none'
        })
        // 直接跳转到编辑页面
        wx.navigateTo({
          url: '/pages/edit/edit?type=brick'
        })
        return
      }

      const result = await apiService.addBrick(brickData)

      if (result.success) {
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        })

        // 重新加载数据
        this.loadBricksList()
      }
    } catch (error) {
      console.error('添加积木失败:', error)
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      })
    }
  },

  // 上传简历 - 增加重试限制
  async uploadResume(retryCount = 0) {
    const maxRetries = 3; // 最多重试3次
    console.log(`🚀 开始上传简历（第${retryCount + 1}次尝试）`);

    if (retryCount >= maxRetries) {
      wx.showToast({
        title: '重试次数过多，请稍后再试',
        icon: 'none',
        duration: 3000
      });
      return;
    }

    try {
      // 选择文件
      const chooseResult = await new Promise((resolve, reject) => {
        wx.chooseMessageFile({
          count: 1,
          type: 'file',
          extension: ['pdf', 'doc', 'docx'],
          success: resolve,
          fail: reject
        })
      })

      if (!chooseResult.tempFiles || chooseResult.tempFiles.length === 0) {
        wx.showToast({
          title: '未选择文件',
          icon: 'none'
        })
        return
      }

      const file = chooseResult.tempFiles[0]
      console.log('📄 选择的文件:', file)

      // 检查文件类型
      const allowedTypes = ['pdf', 'doc', 'docx']
      const fileExtension = file.name.split('.').pop().toLowerCase()

      if (!allowedTypes.includes(fileExtension)) {
        wx.showToast({
          title: '仅支持PDF、DOC、DOCX格式',
          icon: 'none'
        })
        return
      }

      // 检查文件大小（限制10MB）
      if (file.size > 10 * 1024 * 1024) {
        wx.showToast({
          title: '文件大小不能超过10MB',
          icon: 'none'
        })
        return
      }

      this.setData({ uploading: true })
      wx.showLoading({ title: '正在使用AI分析您的简历，预计需要1-2分钟...' })

      // 上传文件到云存储
      const uploadResult = await this.uploadFileToCloud(file)
      console.log('☁️ 文件上传结果:', uploadResult)

      // 调用resumeWorker SCF函数解析简历
      const parseResult = await this.parseResumeWithSCF(uploadResult.fileID, file.name)
      console.log('🤖 简历解析结果:', parseResult)

      if (parseResult.success) {
        // 修复：正确获取解析结果数据
        const resumeData = parseResult.result || parseResult.data || parseResult
        console.log('📊 准备转换的简历数据:', resumeData)

        // 将解析结果转换为积木库数据
        const newBricks = await this.convertResumeDataToBricks(resumeData)

        wx.hideLoading()
        wx.showToast({
          title: `简历解析成功，新增${newBricks.length}个积木`,
          icon: 'success'
        })

        // 不要重新从服务器加载数据，只更新页面显示
        // this.loadBricksList() // 注释掉，避免覆盖刚转换的数据

        // 直接更新统计和筛选
        this.updateCounts()
        this.filterBricks()

        console.log('✅ 简历解析和积木转换完成，当前积木数量:', this.data.bricks.length)
      } else {
        throw new Error(parseResult.error || '简历解析失败')
      }

    } catch (error) {
      console.error('❌ 上传简历失败:', error)
      wx.hideLoading()

      // 根据错误类型提供不同的处理建议
      let errorMessage = '上传失败'
      let showRetry = false

      if (error.message) {
        if (error.message.includes('网络')) {
          errorMessage = '网络连接异常，请检查网络后重试'
          showRetry = true
        } else if (error.message.includes('超时')) {
          errorMessage = '上传超时，请稍后重试'
          showRetry = true
        } else if (error.message.includes('文件')) {
          errorMessage = '文件处理失败，请选择其他文件'
        } else if (error.message.includes('解析')) {
          errorMessage = '简历解析失败，请检查文件内容'
        } else if (error.message.includes('HttpApiService')) {
          errorMessage = '服务暂时不可用，请稍后重试'
          showRetry = true
        } else {
          errorMessage = error.message
        }
      }

      if (showRetry && retryCount < maxRetries) {
        wx.showModal({
          title: '上传失败',
          content: `${errorMessage}\n\n剩余重试次数：${maxRetries - retryCount}`,
          confirmText: '重试',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 用户选择重试，传递重试计数
              setTimeout(() => {
                this.uploadResume(retryCount + 1)
              }, 2000) // 增加重试间隔到2秒
            }
          }
        })
      } else if (showRetry && retryCount >= maxRetries) {
        wx.showToast({
          title: '重试次数已达上限，请稍后再试',
          icon: 'none',
          duration: 3000
        })
      } else {
        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        })
      }
    } finally {
      this.setData({ uploading: false })
    }
  },

  // 上传文件到云存储
  async uploadFileToCloud(file) {
    try {
      const cloudPath = `resumes/${Date.now()}-${file.name}`

      const result = await new Promise((resolve, reject) => {
        wx.cloud.uploadFile({
          cloudPath: cloudPath,
          filePath: file.path,
          success: resolve,
          fail: reject
        })
      })

      console.log('☁️ 文件上传到云存储成功:', result)
      return result

    } catch (error) {
      console.error('❌ 上传文件到云存储失败:', error)
      throw new Error('文件上传失败')
    }
  },

  // 调用resumeWorker SCF函数解析简历 - 增强调试版
  async parseResumeWithSCF(fileID, fileName) {
    try {
      console.log('📡 调用resumeWorker SCF函数解析简历')
      console.log('参数:', { fileID, fileName })

      // 获取HttpApiService
      const HttpApiService = getHttpApiService()
      if (!HttpApiService) {
        throw new Error('HttpApiService不可用')
      }

      // 🔧 修复：正确处理不同文件类型的内容提取
      let resumeContent = null
      const fileType = fileName.split('.').pop().toLowerCase()

      try {
        const downloadResult = await new Promise((resolve, reject) => {
          wx.cloud.downloadFile({
            fileID: fileID,
            success: resolve,
            fail: reject
          })
        })

        if (downloadResult.tempFilePath) {
          console.log('📄 开始提取文件内容，文件类型:', fileType)

          if (fileType === 'pdf') {
            // PDF文件需要特殊处理，不能直接用UTF-8读取
            console.log('⚠️ PDF文件检测到，跳过前端文本提取，交由后端处理')
            resumeContent = null // 让后端处理PDF
          } else if (fileType === 'txt') {
            // 文本文件可以直接读取
            const fileSystemManager = wx.getFileSystemManager()
            const fileData = fileSystemManager.readFileSync(downloadResult.tempFilePath, 'utf8')
            resumeContent = fileData
            console.log('📄 成功读取文本文件内容，长度:', resumeContent.length)
          } else {
            // DOC/DOCX等其他格式也交由后端处理
            console.log('⚠️ Word文档检测到，跳过前端文本提取，交由后端处理')
            resumeContent = null
          }
        }
      } catch (downloadError) {
        console.warn('⚠️ 无法下载文件内容，将使用fileID方式:', downloadError)
      }

      let result;

      // 修复：使用异步架构，调用resumeTaskSubmitter而不是直接调用resumeWorker
      console.log('📡 使用异步架构提交简历解析任务')

      // 提交异步任务
      const taskResult = await wx.cloud.callFunction({
        name: 'resumeTaskSubmitter',
        data: {
          fileId: fileID,
          fileName: fileName,
          fileType: fileName.split('.').pop().toLowerCase(),
          userId: wx.getStorageSync('userId') || 'anonymous'
        }
      });

      console.log('📋 任务提交结果:', taskResult);

      if (!taskResult.result || !taskResult.result.body) {
        throw new Error('任务提交失败');
      }

      const taskResponse = JSON.parse(taskResult.result.body);
      if (!taskResponse.success) {
        throw new Error(taskResponse.error || '任务提交失败');
      }

      const taskId = taskResponse.data.taskId;
      console.log('✅ 任务提交成功，任务ID:', taskId);

      // 轮询任务状态
      result = await this.pollTaskStatus(taskId);
      console.log('✅ 异步任务完成，结果:', result);

      // 详细分析返回结果
      console.log('🔍 详细分析SCF返回结果:')
      console.log('- result.success:', result.success)
      console.log('- result.data:', result.data)
      console.log('- result.result:', result.result)
      console.log('- result类型:', typeof result)

      if (result.data) {
        console.log('🔍 result.data详细结构:')
        console.log('- personalInfo:', result.data.personalInfo)
        console.log('- education:', result.data.education)
        console.log('- workExperience:', result.data.workExperience)
        console.log('- skills:', result.data.skills)
      }

      return result

    } catch (error) {
      console.error('❌ 异步简历解析失败:', error)
      throw new Error('异步简历解析失败: ' + error.message)
    }
  },

  // 使用全局轮询管理器 - 修复高频轮询问题
  async pollTaskStatus(taskId) {
    console.log('🔄 使用全局轮询管理器查询任务状态:', taskId);

    // 引入轮询管理器
    const pollingManager = require('../../utils/polling-manager.js');

    return new Promise((resolve, reject) => {
      // 进度回调
      const onProgress = (task) => {
        console.log(`📋 任务进度更新:`, task);

        // 更新用户界面
        const progressMessage = task.status === 'pending'
          ? 'AI正在准备解析简历...'
          : task.progress || 'AI正在深度分析简历内容...';

        wx.showLoading({
          title: progressMessage,
          mask: true
        });
      };

      // 完成回调
      const onComplete = (task) => {
        wx.hideLoading();
        console.log('✅ 任务完成，返回结果');
        resolve(task.result);
      };

      // 错误回调
      const onError = (error) => {
        wx.hideLoading();
        console.error('❌ 轮询失败:', error);
        reject(error);
      };

      // 启动轮询
      const started = pollingManager.startPolling(taskId, onProgress, onComplete, onError);

      if (!started) {
        // 如果轮询已存在，等待现有轮询完成
        console.log('⚠️ 任务已在轮询中，等待现有轮询完成...');
        wx.showLoading({
          title: '任务处理中，请稍候...',
          mask: true
        });

        // 设置超时处理
        setTimeout(() => {
          wx.hideLoading();
          reject(new Error('任务轮询超时，请稍后重试'));
        }, 60000); // 1分钟超时
      }
    });

  },

  // 将简历数据转换为积木库数据 - 增强版
  async convertResumeDataToBricks(resumeData) {
    try {
      console.log('🔄 开始转换简历数据为积木库数据:', resumeData)

      // 修复：增强数据访问的健壮性，处理各种可能的数据格式
      if (!resumeData) {
        throw new Error('简历数据为空')
      }

      // 适配多种可能的数据格式 - 增强版
      let actualData = resumeData

      // 尝试多种数据路径
      const possiblePaths = [
        resumeData,
        resumeData.data,
        resumeData.result,
        resumeData.response?.data,
        resumeData.body?.data,
        resumeData.content,
        resumeData.analysisResult
      ];

      for (const path of possiblePaths) {
        if (path && typeof path === 'object' && (path.personalInfo || path.workExperience || path.education)) {
          actualData = path;
          console.log('✅ 找到有效数据路径:', path);
          break;
        }
      }

      console.log('📊 实际使用的数据结构:', actualData)

      if (!actualData || typeof actualData !== 'object') {
        throw new Error('无效的简历数据格式')
      }

      const newBricks = []
      const timestamp = new Date().toISOString()

      // 1. 个人信息积木 - 增强处理
      const personalInfo = actualData.personalInfo || actualData.personal || actualData.basicInfo;
      if (personalInfo) {
        const personalBrick = {
          id: `personal_${Date.now()}`,
          title: '个人信息',
          description: `姓名：${personalInfo.name || '未知'}\n联系方式：${personalInfo.phone || ''} ${personalInfo.email || ''}\n地址：${personalInfo.location || ''}\n职位：${personalInfo.title || ''}`,
          category: 'personal',
          keywords: ['个人信息', '联系方式', personalInfo.name].filter(Boolean),
          confidence: 1.0,
          usageCount: 0,
          level: '基础',
          createTime: timestamp,
          updateTime: timestamp,
          source: 'resume_upload',
          data: personalInfo
        }
        newBricks.push(personalBrick)
        console.log('✅ 创建个人信息积木')
      } else {
        console.warn('⚠️ 未找到个人信息数据')
      }

      // 2. 教育背景积木 - 增强处理
      const education = actualData.education || actualData.educationBackground || actualData.schools;
      if (education && Array.isArray(education) && education.length > 0) {
        education.forEach((edu, index) => {
          const educationBrick = {
            id: `education_${Date.now()}_${index}`,
            title: `${edu.school || edu.institution || '学校'} - ${edu.major || edu.degree || '专业'}`,
            description: `学历：${edu.degree || ''}\n时间：${edu.duration || edu.period || ''}\n专业：${edu.major || ''}\n荣誉：${edu.honors ? edu.honors.join(', ') : ''}`,
            category: 'education',
            keywords: ['教育背景', edu.degree, edu.major, edu.school].filter(Boolean),
            confidence: 0.9,
            usageCount: 0,
            level: '基础',
            createTime: timestamp,
            updateTime: timestamp,
            source: 'resume_upload',
            data: edu
          }
          newBricks.push(educationBrick)
          console.log(`✅ 创建教育背景积木: ${educationBrick.title}`)
        })
      } else {
        console.warn('⚠️ 未找到教育背景数据或数据格式不正确:', education)
      }

      // 3. 工作经历积木 - 修复：与云函数保持一致
      const workExperience = actualData.workExperience || actualData.experience || actualData.work || actualData.jobs;
      if (workExperience && Array.isArray(workExperience) && workExperience.length > 0) {
        workExperience.forEach((work, index) => {
          // 3.1 创建工作经历基础积木（仅包含基本信息）
          const workBasicBrick = {
            id: `work_basic_${Date.now()}_${index}`,
            title: `${work.position || work.title || work.role || '职位'} - ${work.company || work.employer || '公司'}`,
            description: `职位：${work.position || work.title || work.role || ''}\n公司：${work.company || work.employer || ''}\n时间：${work.duration || work.period || ''}`,
            category: 'experience',
            keywords: [work.company || '', work.position || '', '工作经历'].filter(Boolean),
            confidence: 0.9,
            usageCount: 0,
            level: '基础',
            createTime: timestamp,
            updateTime: timestamp,
            source: 'resume_upload',
            data: {
              company: work.company || work.employer,
              position: work.position || work.title || work.role,
              duration: work.duration || work.period,
              type: 'basic_info'
            }
          }
          newBricks.push(workBasicBrick)
          console.log(`✅ 创建工作经历基础积木: ${workBasicBrick.title}`)

          // 3.2 从工作经历中提取具体项目作为能力积木
          const responsibilities = work.responsibilities || [];
          if (Array.isArray(responsibilities) && responsibilities.length > 0) {
            responsibilities.forEach((responsibility, respIndex) => {
              if (responsibility && typeof responsibility === 'string' && responsibility.trim()) {
                const projectKeywords = this.extractProjectKeywords(responsibility, work.company, work.position);

                // 使用备用方案生成项目标题（避免async/await问题）
                const projectTitle = this.generateProjectTitleFallback(responsibility, work.position);

                const projectBrick = {
                  id: `project_${Date.now()}_${index}_${respIndex}`,
                  title: projectTitle,
                  description: responsibility,
                  category: 'project', // 关键：使用project分类
                  keywords: projectKeywords.abilities,
                  confidence: 0.85,
                  usageCount: 0,
                  level: this.determineProjectLevel(responsibility),
                  createTime: timestamp,
                  updateTime: timestamp,
                  source: 'resume_upload',
                  data: {
                    project: responsibility,
                    company: work.company || work.employer,
                    position: work.position || work.title || work.role,
                    duration: work.duration || work.period,
                    abilities: projectKeywords.abilities,
                    companyTag: projectKeywords.companyTag
                  }
                }
                newBricks.push(projectBrick)
                console.log(`✅ 创建项目积木: ${projectBrick.title}`)
              }
            });
          }

          // 3.3 处理成就作为独立的能力积木
          const achievements = work.achievements || [];
          if (Array.isArray(achievements) && achievements.length > 0) {
            achievements.forEach((achievement, achIndex) => {
              if (achievement && typeof achievement === 'string' && achievement.trim()) {
                const achievementKeywords = this.extractProjectKeywords(achievement, work.company, work.position);

                // 使用备用方案生成成就标题（避免async/await问题）
                const achievementTitle = this.generateProjectTitleFallback(achievement, work.position);

                const achievementBrick = {
                  id: `achievement_${Date.now()}_${index}_${achIndex}`,
                  title: achievementTitle,
                  description: achievement,
                  category: 'project', // 关键：使用project分类
                  keywords: achievementKeywords.abilities,
                  confidence: 0.9,
                  usageCount: 0,
                  level: '高级',
                  createTime: timestamp,
                  updateTime: timestamp,
                  source: 'resume_upload',
                  data: {
                    project: achievement,
                    company: work.company || work.employer,
                    position: work.position || work.title || work.role,
                    duration: work.duration || work.period,
                    abilities: achievementKeywords.abilities,
                    companyTag: achievementKeywords.companyTag,
                    type: 'achievement'
                  }
                }
                newBricks.push(achievementBrick)
                console.log(`✅ 创建成就积木: ${achievementBrick.title}`)
              }
            });
          }
        })
      } else {
        console.warn('⚠️ 未找到工作经历数据或数据格式不正确:', workExperience)
      }

      // 4. 技能积木 - 已移除（用户要求去掉技能特长积木）
      // const skills = actualData.skills || actualData.skillSet || actualData.abilities;
      // 技能信息已经通过项目积木的能力标签体现，不再单独生成技能积木
      console.log('⚠️ 已跳过技能积木生成（改为通过项目积木的能力标签体现）')

      // 5. 项目经历积木
      if (actualData.projects && Array.isArray(actualData.projects)) {
        actualData.projects.forEach((project, index) => {
          const projectBrick = {
            id: `project_${Date.now()}_${index}`,
            title: project.name || `项目${index + 1}`,
            description: `${project.description || ''}\n技术栈：${project.technologies ? project.technologies.join(', ') : ''}\n成就：${project.achievements ? project.achievements.join('\n') : ''}`,
            category: 'project',
            keywords: [
              '项目经历',
              project.name,
              ...(Array.isArray(project.technologies) ? project.technologies.filter(t => typeof t === 'string') : [])
            ].filter(Boolean),
            confidence: 0.85,
            usageCount: 0,
            level: '高级',
            createTime: timestamp,
            updateTime: timestamp,
            source: 'resume_upload',
            data: project
          }
          newBricks.push(projectBrick)
        })
      }

      // 6. 成就积木
      if (resumeData.achievements && Array.isArray(resumeData.achievements)) {
        resumeData.achievements.forEach((achievement, index) => {
          const achievementBrick = {
            id: `achievement_${Date.now()}_${index}`,
            title: achievement.title || `成就${index + 1}`,
            description: achievement.description || '',
            category: 'achievement',
            keywords: ['成就', '荣誉', achievement.title].filter(Boolean),
            confidence: 0.9,
            usageCount: 0,
            level: '高级',
            createTime: timestamp,
            updateTime: timestamp,
            source: 'resume_upload',
            data: achievement
          }
          newBricks.push(achievementBrick)
        })
      }

      // 建立技能与工作经历的关联关系
      this.establishSkillExperienceRelations(newBricks)

      // 🔧 修复：通过状态管理器统一保存数据，避免数据不一致
      const existingBricks = this.data.bricks || []

      // 去重逻辑：基于内容相似度去重，避免重复积木
      const deduplicatedBricks = this.deduplicateBricks(existingBricks, newBricks)

      console.log(`📊 去重前: 现有${existingBricks.length}个，新增${newBricks.length}个`)
      console.log(`📊 去重后: 总计${deduplicatedBricks.length}个积木`)

      // 更新页面数据
      this.setData({
        bricks: deduplicatedBricks,
        filteredBricks: deduplicatedBricks
      })

      // 🔧 修复：通过状态管理器保存数据，确保数据一致性
      const app = getApp()
      const store = app.getStore()

      if (store) {
        // 使用状态管理器保存，会自动同步到全局数据和本地存储
        store.setBricks(deduplicatedBricks)
        console.log('✅ 积木数据已通过状态管理器保存')
      } else {
        // 兼容处理：直接保存到全局数据和本地存储
        app.globalData.bricks = deduplicatedBricks
        wx.setStorageSync('bricks', deduplicatedBricks)
        console.log('✅ 积木数据已直接保存到全局数据和本地存储')
      }

      console.log(`✅ 成功转换并创建了 ${newBricks.length} 个积木`)
      console.log('积木分类统计:')
      console.log('- 个人信息:', newBricks.filter(b => b.category === 'personal').length)
      console.log('- 教育背景:', newBricks.filter(b => b.category === 'education').length)
      console.log('- 工作经历:', newBricks.filter(b => b.category === 'experience').length)
      console.log('- 项目能力:', newBricks.filter(b => b.category === 'project').length)
      console.log('- 技能:', newBricks.filter(b => b.category === 'skills').length)

      // 立即更新统计数据
      this.updateCounts()
      this.filterBricks()

      return newBricks

    } catch (error) {
      console.error('❌ 转换简历数据失败:', error)
      throw new Error('数据转换失败: ' + error.message)
    }
  },

  // 获取技能类型
  getSkillType(skillName, skillsObj) {
    if (skillsObj.technical && skillsObj.technical.includes(skillName)) {
      return 'technical'
    } else if (skillsObj.soft && skillsObj.soft.includes(skillName)) {
      return 'soft'
    } else if (skillsObj.languages && skillsObj.languages.includes(skillName)) {
      return 'languages'
    } else if (skillsObj.tools && skillsObj.tools.includes(skillName)) {
      return 'tools'
    }
    return 'technical' // 默认为技术技能
  },

  // 从项目描述中提取关键词
  extractProjectKeywords(description, company, position) {
    // 能力关键词库
    const abilityKeywords = {
      '管理': ['管理', '运营', '团队', '领导', '协调', '规划'],
      '数据分析': ['数据', '分析', '统计', '指标', '报告', '监控'],
      '产品': ['产品', '功能', '需求', '设计', '优化', '迭代'],
      '营销': ['营销', '推广', '获客', '转化', '增长', '渠道'],
      '技术': ['开发', '技术', '系统', '平台', '工具', '架构'],
      '沟通': ['沟通', '协作', '合作', '对接', '联系', '交流'],
      '创新': ['创新', '改进', '优化', '提升', '突破', '创造'],
      '执行': ['执行', '落地', '实施', '推进', '完成', '达成']
    };

    const foundAbilities = [];
    const descLower = description.toLowerCase();

    // 提取能力标签
    Object.entries(abilityKeywords).forEach(([ability, keywords]) => {
      if (keywords.some(keyword => descLower.includes(keyword))) {
        foundAbilities.push(ability);
      }
    });

    // 确保至少有4个能力标签，不足的用通用标签补充
    const defaultAbilities = ['执行力', '专业能力', '团队协作', '问题解决'];
    while (foundAbilities.length < 4) {
      const defaultAbility = defaultAbilities[foundAbilities.length % defaultAbilities.length];
      if (!foundAbilities.includes(defaultAbility)) {
        foundAbilities.push(defaultAbility);
      }
    }

    // 只取前4个
    const abilities = foundAbilities.slice(0, 4);

    // 生成公司标签（用于排序和识别）
    const companyTag = `${company || '未知公司'}_${position || '未知职位'}`;

    return {
      abilities,
      companyTag
    };
  },

  // 生成项目标题 - 同步版本（使用备用方案）
  generateProjectTitle(description, position) {
    // 直接使用备用方案，避免async/await问题
    return this.generateProjectTitleFallback(description, position);
  },

  // 生成项目标题 - AI分析版（异步，供后续优化使用）
  async generateProjectTitleWithAI(description, position) {
    try {
      // 使用AI分析项目描述，生成准确的标题
      const aiTitle = await this.analyzeProjectWithAI(description, position);
      if (aiTitle && aiTitle.trim()) {
        return aiTitle.trim();
      }
    } catch (error) {
      console.warn('⚠️ AI标题生成失败，使用备用方案:', error);
    }

    // 备用方案：基于规则的标题生成
    return this.generateProjectTitleFallback(description, position);
  },

  // AI分析项目描述
  async analyzeProjectWithAI(description, position) {
    try {
      // 创建AI模型实例
      const model = wx.cloud.extend.AI.createModel("deepseek");

      const systemPrompt = `你是一个专业的简历分析师。请根据项目描述，生成一个简洁准确的项目标题。

要求：
1. 标题要体现项目的核心能力和业务价值
2. 长度控制在8个字以内
3. 格式：[核心能力][业务领域][经验/能力]
4. 例如：跨境商家管理经验、商家赋能体系搭建、广告SOP搭建经验

参考示例：
- "管理全年8w+的跨境卖家全生命周期" → "跨境商家管理经验"
- "搭建多层次的商家赋能体系" → "商家赋能体系搭建"
- "助商家ACOS降低35%" → "广告SOP搭建经验"

请只返回标题，不要其他内容。`;

      const userInput = `职位：${position}\n项目描述：${description}`;

      const res = await model.streamText({
        data: {
          model: "deepseek-v3",
          messages: [
            { role: "system", content: systemPrompt },
            { role: "user", content: userInput }
          ],
        },
      });

      let aiResponse = '';
      for await (let str of res.textStream) {
        aiResponse += str;
      }

      // 清理AI返回的结果
      const cleanTitle = aiResponse
        .replace(/["""'']/g, '')
        .replace(/^[：:：\s]+/, '')
        .replace(/[：:：\s]+$/, '')
        .trim();

      console.log(`🤖 AI生成标题: "${cleanTitle}" (原描述: ${description.substring(0, 30)}...)`);

      return cleanTitle;
    } catch (error) {
      console.error('❌ AI标题生成失败:', error);
      throw error;
    }
  },

  // 备用标题生成方案 - 优化版
  generateProjectTitleFallback(description, position) {
    // 清理描述文本
    const cleanDesc = description.replace(/[，。！？；：""''（）【】]/g, ' ').trim();

    // 专业领域识别 - 扩展版
    const domainMappings = {
      '跨境商家': ['跨境', '亚马逊', '海外', '国际', '出口', '外贸', '卖家'],
      '商家赋能': ['商家', '赋能', '卖家', '商户', '店铺'],
      '广告SOP': ['ACOS', 'ROI', 'CPC', 'CPM', '广告', '投放', 'SOP', '降低'],
      '数据分析': ['数据', '分析', '指标', '报告', '统计', '监控'],
      '产品推广': ['产品', '推广', '推出', '上线', '发布'],
      '营销预算': ['营销', '预算', '费用', '成本', '投入'],
      '用户运营': ['用户', '运营', '活动', '社群', '内容', '流量'],
      '团队管理': ['管理', '团队', '领导', '协调', '规划', '人员'],
      '技术开发': ['开发', '技术', '系统', '平台', '工具', '架构'],
      '客户服务': ['客户', '服务', '支持', '维护', '满意度']
    };

    // 核心能力识别 - 扩展版
    const abilityMappings = {
      '管理经验': ['管理', '全生命周期', '累计', '孵化', '名下'],
      '体系搭建': ['搭建', '建立', '构建', '创建', '建设', '体系', '框架', '多层次'],
      '搭建经验': ['ACOS降低', '页面完整度', '提升', 'SOP'],
      '策略制定': ['策略', '方案', '规划', '计划', '制定', '设计'],
      '预算管理': ['预算', '管理', '年度', '万', '亿'],
      '项目执行': ['执行', '实施', '落地', '推进', '完成', '达成', '推出'],
      '系统开发': ['开发', '系统', '建立', '完善'],
      '流程建立': ['流程', '建立', '完善', '服务']
    };

    // 识别专业领域
    let domain = '';
    for (const [key, keywords] of Object.entries(domainMappings)) {
      if (keywords.some(keyword => cleanDesc.includes(keyword))) {
        domain = key;
        break;
      }
    }

    // 识别核心能力
    let ability = '';
    for (const [key, keywords] of Object.entries(abilityMappings)) {
      if (keywords.some(keyword => cleanDesc.includes(keyword))) {
        ability = key;
        break;
      }
    }

    // 智能生成标题
    let title = '';

    // 优先级1：领域 + 能力
    if (domain && ability) {
      title = `${domain}${ability}`;
    }
    // 优先级2：仅领域
    else if (domain) {
      title = `${domain}运营经验`;
    }
    // 优先级3：仅能力
    else if (ability) {
      title = ability;
    }
    // 优先级4：基于动作和对象的通用方案
    else {
      const actionKeywords = cleanDesc.match(/(负责|管理|运营|开发|设计|优化|提升|增长|创建|搭建|实现|建立|推进|执行|完成|达成|助力|帮助|支持|协调|策划|制定|落地|推出|上线)/g);
      const businessObjects = cleanDesc.match(/(客户|用户|商家|卖家|产品|系统|平台|项目|团队|业务|市场|销售|营销|推广|运营|数据|分析|策略|方案|流程|体系|工具|功能|服务|渠道|社群)/g);

      if (actionKeywords && businessObjects) {
        const action = actionKeywords[0];
        const object = businessObjects[0];
        title = `${action}${object}经验`;
      } else if (actionKeywords) {
        title = `${actionKeywords[0]}能力经验`;
      } else {
        title = `${position || '核心'}经验`;
      }
    }

    // 确保标题长度合理
    if (title.length > 8) {
      title = title.substring(0, 8);
    }

    return title;
  },

  // 判断项目等级
  determineProjectLevel(description) {
    const desc = description.toLowerCase();

    // 高级项目指标
    const advancedIndicators = ['亿', '千万', '百万', '年销', '总gms', '卓越', '突破'];
    // 中级项目指标
    const intermediateIndicators = ['万', '千', '提升', '优化', '改进', '增长'];

    if (advancedIndicators.some(indicator => desc.includes(indicator))) {
      return '高级';
    } else if (intermediateIndicators.some(indicator => desc.includes(indicator))) {
      return '中级';
    } else {
      return '基础';
    }
  },

  // 积木去重函数
  deduplicateBricks(existingBricks, newBricks) {
    const allBricks = [...existingBricks];

    newBricks.forEach(newBrick => {
      // 检查是否已存在相似的积木
      const isDuplicate = existingBricks.some(existingBrick => {
        // 1. 相同分类的积木才比较
        if (existingBrick.category !== newBrick.category) {
          return false;
        }

        // 2. 个人信息和教育背景：按分类去重（每种只保留一个）
        if (newBrick.category === 'personal' || newBrick.category === 'education') {
          return true; // 已存在同类积木，跳过
        }

        // 3. 工作经历：按公司+职位去重
        if (newBrick.category === 'experience') {
          const existingCompany = existingBrick.data?.company || '';
          const existingPosition = existingBrick.data?.position || '';
          const newCompany = newBrick.data?.company || '';
          const newPosition = newBrick.data?.position || '';

          return existingCompany === newCompany && existingPosition === newPosition;
        }

        // 4. 项目积木：按描述相似度去重
        if (newBrick.category === 'project') {
          const similarity = this.calculateTextSimilarity(
            existingBrick.description || '',
            newBrick.description || ''
          );
          return similarity > 0.8; // 相似度超过80%认为是重复
        }

        // 5. 其他类型：按标题去重
        return existingBrick.title === newBrick.title;
      });

      if (!isDuplicate) {
        allBricks.push(newBrick);
        console.log(`✅ 添加新积木: ${newBrick.title}`);
      } else {
        console.log(`⚠️ 跳过重复积木: ${newBrick.title}`);
      }
    });

    return allBricks;
  },

  // 计算文本相似度
  calculateTextSimilarity(text1, text2) {
    if (!text1 || !text2) return 0;

    // 简化文本：去除标点符号，转小写
    const clean1 = text1.replace(/[^\w\s]/g, '').toLowerCase();
    const clean2 = text2.replace(/[^\w\s]/g, '').toLowerCase();

    if (clean1 === clean2) return 1;

    // 计算字符级别的相似度
    const longer = clean1.length > clean2.length ? clean1 : clean2;
    const shorter = clean1.length > clean2.length ? clean2 : clean1;

    if (longer.length === 0) return 1;

    // 计算编辑距离
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  },

  // 计算编辑距离
  levenshteinDistance(str1, str2) {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  },

  // 建立技能与工作经历的关联关系
  establishSkillExperienceRelations(bricks) {
    try {
      console.log('🔗 开始建立技能与工作经历的关联关系')

      // 分类积木
      const skillBricks = bricks.filter(brick => brick.category === 'skills')
      const experienceBricks = bricks.filter(brick => brick.category === 'experience')

      console.log(`找到 ${skillBricks.length} 个技能积木，${experienceBricks.length} 个工作经历积木`)

      // 为每个技能积木找到相关的工作经历
      skillBricks.forEach(skill => {
        const relatedExperiences = []

        experienceBricks.forEach(experience => {
          // 检查技能关键词是否在工作经历中出现
          const skillKeywords = skill.keywords || []
          const experienceText = `${experience.title} ${experience.description}`.toLowerCase()

          const isRelated = skillKeywords.some(keyword =>
            experienceText.includes(keyword.toLowerCase())
          )

          if (isRelated) {
            relatedExperiences.push(experience.title)
          }
        })

        skill.relatedExperience = relatedExperiences
        console.log(`技能 "${skill.title}" 关联到 ${relatedExperiences.length} 个工作经历`)
      })

      // 为每个工作经历积木找到相关的技能
      experienceBricks.forEach(experience => {
        const relatedSkills = []

        skillBricks.forEach(skill => {
          // 检查技能是否与该工作经历相关
          if (skill.relatedExperience && skill.relatedExperience.includes(experience.title)) {
            relatedSkills.push(skill.title)
          }
        })

        experience.relatedSkills = relatedSkills
        console.log(`工作经历 "${experience.title}" 关联到 ${relatedSkills.length} 个技能`)
      })

      // 基于技能匹配度进行智能关联
      this.performIntelligentSkillMatching(skillBricks, experienceBricks)

      console.log('✅ 技能与工作经历关联关系建立完成')

    } catch (error) {
      console.error('❌ 建立关联关系失败:', error)
    }
  },

  // 执行智能技能匹配
  performIntelligentSkillMatching(skillBricks, experienceBricks) {
    try {
      console.log('🤖 执行智能技能匹配')

      // 常见技能关键词映射
      const skillMappings = {
        'javascript': ['js', 'javascript', '前端', 'web', 'react', 'vue', 'angular'],
        'python': ['python', 'django', 'flask', '数据分析', '机器学习'],
        'java': ['java', 'spring', 'springboot', '后端', 'maven'],
        'react': ['react', 'jsx', '前端', 'javascript', 'web'],
        'vue': ['vue', 'vuejs', '前端', 'javascript', 'web'],
        'node': ['node', 'nodejs', '后端', 'javascript', 'express'],
        '管理': ['管理', '领导', '团队', '项目管理', '人员管理'],
        '沟通': ['沟通', '协调', '合作', '团队合作', '客户沟通']
      }

      skillBricks.forEach(skill => {
        const skillTitle = skill.title.toLowerCase()

        experienceBricks.forEach(experience => {
          const experienceText = `${experience.title} ${experience.description}`.toLowerCase()

          // 检查技能映射
          for (const [key, mappings] of Object.entries(skillMappings)) {
            if (skillTitle.includes(key) || mappings.some(mapping => skillTitle.includes(mapping))) {
              const hasMatch = mappings.some(mapping => experienceText.includes(mapping))

              if (hasMatch) {
                // 添加关联（避免重复）
                if (!skill.relatedExperience.includes(experience.title)) {
                  skill.relatedExperience.push(experience.title)
                }
                if (!experience.relatedSkills.includes(skill.title)) {
                  experience.relatedSkills.push(skill.title)
                }
              }
            }
          }
        })
      })

      console.log('✅ 智能技能匹配完成')

    } catch (error) {
      console.error('❌ 智能技能匹配失败:', error)
    }
  },

  // 刷新数据
  refreshData() {
    this.loadBricksList()
    wx.stopPullDownRefresh()
  },

  // 加载更多
  loadMore() {
    // 这里可以实现分页加载逻辑
    this.setData({ hasMore: false })
  },

  // 创建简历
  createResume() {
    wx.navigateTo({
      url: '/pages/generate/generate',
      success: () => {
        console.log('跳转到简历生成页面')
      },
      fail: (err) => {
        console.error('跳转失败:', err)
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        })
      }
    })
  },

  // 检查登录状态 - 修复版本，避免循环跳转
  checkLoginStatus() {
    try {
      console.log('🔍 [bricks] 开始检查登录状态...');

      // 使用统一的登录状态管理器
      const LoginStateManager = global.LoginStateManager || getApp().LoginStateManager || require('../../utils/login-state-manager.js');
      const manager = new LoginStateManager();

      // 使用宽松的检查模式，避免误判
      const isLoggedIn = manager.checkLoginStatusSafely('pages/bricks/bricks');

      console.log(`🔍 [bricks] 登录状态检查结果: ${isLoggedIn}`);

      // 只有在确实未登录时才跳转，并添加防护机制
      if (!isLoggedIn) {
        console.log('⚠️ [bricks] 用户未登录，准备跳转到登录页面');

        // 添加延迟，避免时序问题
        setTimeout(() => {
          // 再次检查，避免时序问题导致的误判
          const recheckResult = manager.checkLoginStatusSafely('pages/bricks/bricks');
          console.log(`🔍 [bricks] 延迟重检结果: ${recheckResult}`);

          if (!recheckResult) {
            console.log('🔄 [bricks] 确认未登录，执行跳转');
            // 使用switchTab跳转到首页，避免navigateTo超时
            wx.switchTab({
              url: '/pages/generate/generate',
              success: () => {
                console.log('✅ [bricks] 成功跳转到首页，首页会处理登录');
              },
              fail: (err) => {
                console.error('❌ [bricks] 跳转到首页失败:', err);
                // 如果switchTab也失败，尝试reLaunch
                wx.reLaunch({
                  url: '/pages/generate/generate'
                });
              }
            });
          } else {
            console.log('✅ [bricks] 重检发现已登录，取消跳转');
          }
        }, 500); // 500ms延迟，减少等待时间
      } else {
        console.log('✅ [bricks] 用户已登录，继续访问页面');
      }
    } catch (error) {
      console.error('❌ [bricks] 检查登录状态失败:', error);
      // 错误情况下不跳转，避免循环
      console.log('🛡️ [bricks] 登录检查出错，不执行跳转以避免循环');
    }
  },

  // 加载积木数据（备用方法）
  async loadBricks() {
    try {
      const bricks = this.getMockBricks()
      const processedBricks = bricks.map(brick => ({
        ...brick,
        selected: false,
        icon: this.getBrickIcon(brick.category),
        category: this.getCategoryName(brick.category),
        matchCount: brick.usageCount || 0
      }))

      this.setData({
        bricks: processedBricks,
        filteredBricks: processedBricks
      })

      // 更新全局数据
      getApp().globalData.bricks = processedBricks

      this.updateCounts()
      this.filterBricks()
    } catch (error) {
      console.error('加载积木失败:', error)
    }
  },

  // 加载统计数据
  async loadStats() {
    try {
      // 这里可以调用统计API
      console.log('加载统计数据')
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  },

  // 清空所有积木
  clearAllBricks() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有积木数据吗？此操作不可恢复。',
      confirmText: '确认清空',
      cancelText: '取消',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          try {
            // 清空页面数据
            this.setData({
              bricks: [],
              filteredBricks: [],
              resumes: []
            })

            // 通过状态管理器清空数据
            const app = getApp()
            const store = app.getStore && app.getStore()

            if (store) {
              store.setBricks([])
              store.setResumes([])
            } else {
              // 兼容处理：直接清空全局数据和本地存储
              app.globalData.bricks = []
              app.globalData.resumes = []
              wx.removeStorageSync('bricks')
              wx.removeStorageSync('resumes')
            }

            // 更新统计
            this.updateCounts()

            wx.showToast({
              title: '清空成功',
              icon: 'success'
            })

            console.log('✅ 所有积木数据已清空')
          } catch (error) {
            console.error('❌ 清空积木失败:', error)
            wx.showToast({
              title: '清空失败',
              icon: 'error'
            })
          }
        }
      }
    })
  },

  // 确保数据一致性
  ensureDataConsistency() {
    try {
      console.log('🔄 开始确保数据一致性')

      const app = getApp()
      const store = app.getStore && app.getStore()

      if (store) {
        // 使用状态管理器确保数据一致性
        const storeBricks = store.getState('bricks.list') || []
        const storeResumes = store.getState('resumes.list') || []

        // 如果页面数据与状态管理器数据不一致，以状态管理器为准
        const pageBricks = this.data.bricks || []
        const pageResumes = this.data.resumes || []

        if (JSON.stringify(pageBricks) !== JSON.stringify(storeBricks) ||
          JSON.stringify(pageResumes) !== JSON.stringify(storeResumes)) {

          console.log('🔄 检测到数据不一致，同步状态管理器数据到页面')

          this.setData({
            bricks: storeBricks,
            filteredBricks: storeBricks,
            resumes: storeResumes,
            filteredResumes: storeResumes
          })

          // 更新统计和筛选
          this.updateCounts()
          this.filterBricks()
          this.filterResumes()
        }
      } else {
        console.log('⚠️ 状态管理器不可用，跳过数据一致性检查')
      }

      console.log('✅ 数据一致性检查完成')
    } catch (error) {
      console.error('❌ 数据一致性检查失败:', error)
    }
  }
})